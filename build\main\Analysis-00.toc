(['D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\main.py'],
 ['D:\\Archives\\Documents\\CodeSpace\\PandocWebUI'],
 ['uvicorn.lifespan.on',
  'uvicorn.lifespan.off',
  'uvicorn.protocols.websockets.auto',
  'uvicorn.protocols.http.auto',
  'uvicorn.protocols.websockets.websockets_impl',
  'uvicorn.protocols.http.httptools_impl',
  'uvicorn.protocols.http.h11_impl',
  'uvicorn.loops.auto',
  'uvicorn.loops.asyncio',
  'uvicorn.logging',
  'fastapi.openapi.utils',
  'fastapi.openapi.models',
  'email.mime.multipart',
  'email.mime.text',
  'email.mime.base'],
 [('d:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('d:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['tkinter',
  'matplotlib',
  'numpy',
  'scipy',
  'pandas',
  'PIL',
  'PyQt5',
  'PyQt6',
  'PySide2',
  'PySide6',
  'wx',
  'tornado',
  'django',
  'flask',
  'bottle',
  'pyramid',
  'cherrypy',
  'twisted',
  'jupyter',
  'ipython',
  'notebook',
  'spyder',
  'pytest',
  'nose',
  'unittest2',
  'doctest',
  'test',
  'tests',
  '_pytest',
  'setuptools',
  'distutils',
  'pkg_resources',
  '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('static\\index.html',
   'D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\static\\index.html',
   'DATA'),
  ('static\\script.js',
   'D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\static\\script.js',
   'DATA'),
  ('static\\styles.css',
   'D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\static\\styles.css',
   'DATA')],
 '3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('main',
   'D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\main.py',
   'PYSOURCE')],
 [('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextlib.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\signal.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gzip.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\textwrap.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gettext.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fnmatch.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\mimetypes.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getopt.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\utils.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\quopri.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\calendar.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\statistics.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\numbers.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ssl.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\string.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hashlib.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uu.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\optparse.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\header.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bisect.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\client.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextvars.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\base64.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hmac.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\struct.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\selectors.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tempfile.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\inspect.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\py_compile.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\lzma.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bz2.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\csv.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tokenize.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\opcode.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ast.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\runpy.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('email.mime.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\mime\\base.py',
   'PYMODULE'),
  ('email.mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\mime\\__init__.py',
   'PYMODULE'),
  ('email.mime.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\mime\\text.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('fastapi.openapi.models',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\openapi\\models.py',
   'PYMODULE'),
  ('fastapi.openapi',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\openapi\\__init__.py',
   'PYMODULE'),
  ('typing_extensions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('fastapi.logger',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\logger.py',
   'PYMODULE'),
  ('fastapi._compat',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\_compat.py',
   'PYMODULE'),
  ('pydantic.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic._migration',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._internal',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('annotated_types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic.types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic.main',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.networks',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.color',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\colorsys.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.config',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._std_types_schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_std_types_schema.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic.validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ipaddress.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\__future__.py',
   'PYMODULE'),
  ('pydantic.typing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.errors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('fastapi.openapi.constants',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\openapi\\constants.py',
   'PYMODULE'),
  ('fastapi.params',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\params.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('pydantic_core',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.fields',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('starlette.datastructures',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\datastructures.py',
   'PYMODULE'),
  ('starlette',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\__init__.py',
   'PYMODULE'),
  ('starlette.status',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\status.py',
   'PYMODULE'),
  ('starlette.types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\types.py',
   'PYMODULE'),
  ('starlette.concurrency',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\concurrency.py',
   'PYMODULE'),
  ('anyio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio.abc',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('sniffio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio._backends',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio._core',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('idna',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._compat',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_compat.py',
   'PYMODULE'),
  ('pydantic.version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\platform.py',
   'PYMODULE'),
  ('fastapi.types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\types.py',
   'PYMODULE'),
  ('fastapi.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\exceptions.py',
   'PYMODULE'),
  ('starlette.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\exceptions.py',
   'PYMODULE'),
  ('starlette.middleware.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\middleware\\exceptions.py',
   'PYMODULE'),
  ('starlette.middleware',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\middleware\\__init__.py',
   'PYMODULE'),
  ('starlette.websockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\websockets.py',
   'PYMODULE'),
  ('starlette.responses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\responses.py',
   'PYMODULE'),
  ('starlette.background',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\background.py',
   'PYMODULE'),
  ('starlette._compat',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\_compat.py',
   'PYMODULE'),
  ('starlette.requests',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\requests.py',
   'PYMODULE'),
  ('starlette.routing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\routing.py',
   'PYMODULE'),
  ('starlette.convertors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\convertors.py',
   'PYMODULE'),
  ('multipart.multipart',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\multipart\\multipart.py',
   'PYMODULE'),
  ('multipart',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\multipart\\__init__.py',
   'PYMODULE'),
  ('multipart.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\multipart\\exceptions.py',
   'PYMODULE'),
  ('multipart.decoders',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\multipart\\decoders.py',
   'PYMODULE'),
  ('starlette.formparsers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\formparsers.py',
   'PYMODULE'),
  ('starlette._utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\_utils.py',
   'PYMODULE'),
  ('fastapi.openapi.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\openapi\\utils.py',
   'PYMODULE'),
  ('fastapi.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\utils.py',
   'PYMODULE'),
  ('fastapi.encoders',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\encoders.py',
   'PYMODULE'),
  ('fastapi.dependencies.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\dependencies\\utils.py',
   'PYMODULE'),
  ('fastapi.dependencies',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\dependencies\\__init__.py',
   'PYMODULE'),
  ('fastapi.security.open_id_connect_url',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\open_id_connect_url.py',
   'PYMODULE'),
  ('fastapi.security',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\__init__.py',
   'PYMODULE'),
  ('fastapi.security.http',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\http.py',
   'PYMODULE'),
  ('fastapi.security.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\utils.py',
   'PYMODULE'),
  ('fastapi.security.api_key',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\api_key.py',
   'PYMODULE'),
  ('fastapi.security.oauth2',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\oauth2.py',
   'PYMODULE'),
  ('fastapi.param_functions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\param_functions.py',
   'PYMODULE'),
  ('fastapi.security.base',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\base.py',
   'PYMODULE'),
  ('fastapi.concurrency',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\concurrency.py',
   'PYMODULE'),
  ('fastapi.background',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\background.py',
   'PYMODULE'),
  ('fastapi.dependencies.models',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\dependencies\\models.py',
   'PYMODULE'),
  ('fastapi.datastructures',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\datastructures.py',
   'PYMODULE'),
  ('fastapi.routing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\routing.py',
   'PYMODULE'),
  ('uvicorn.logging',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\logging.py',
   'PYMODULE'),
  ('click',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click.parser',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\difflib.py',
   'PYMODULE'),
  ('click.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\glob.py',
   'PYMODULE'),
  ('click._compat',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('colorama',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorama.ansi',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.initialise',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('click._winconsole',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.termui',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tty.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\webbrowser.py',
   'PYMODULE'),
  ('click.globals',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.formatting',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.decorators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.core',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('uvicorn.loops.asyncio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\loops\\asyncio.py',
   'PYMODULE'),
  ('uvicorn.loops',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\loops\\__init__.py',
   'PYMODULE'),
  ('uvicorn.loops.auto',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\loops\\auto.py',
   'PYMODULE'),
  ('uvicorn.loops.uvloop',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\loops\\uvloop.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.h11_impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\h11_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.http',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\__init__.py',
   'PYMODULE'),
  ('uvicorn.server',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\server.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.wsproto_impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\wsproto_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\utils.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.flow_control',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\flow_control.py',
   'PYMODULE'),
  ('uvicorn.config',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\config.py',
   'PYMODULE'),
  ('yaml',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.events',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('dotenv',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.variables',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('dotenv.parser',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('uvicorn.middleware.wsgi',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\middleware\\wsgi.py',
   'PYMODULE'),
  ('uvicorn.middleware',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\middleware\\__init__.py',
   'PYMODULE'),
  ('uvicorn.middleware.proxy_headers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\middleware\\proxy_headers.py',
   'PYMODULE'),
  ('uvicorn.middleware.message_logger',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\middleware\\message_logger.py',
   'PYMODULE'),
  ('uvicorn.middleware.asgi2',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\middleware\\asgi2.py',
   'PYMODULE'),
  ('uvicorn.importer',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\importer.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\config.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\configparser.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socketserver.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\smtplib.py',
   'PYMODULE'),
  ('uvicorn._types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\_types.py',
   'PYMODULE'),
  ('h11._connection',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._writers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('h11._util',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._state',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._readers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._abnf',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._headers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._events',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.httptools_impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\httptools_impl.py',
   'PYMODULE'),
  ('httptools',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\httptools\\__init__.py',
   'PYMODULE'),
  ('httptools._version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\httptools\\_version.py',
   'PYMODULE'),
  ('httptools.parser',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\httptools\\parser\\__init__.py',
   'PYMODULE'),
  ('httptools.parser.errors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\httptools\\parser\\errors.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.websockets_impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\websockets_impl.py',
   'PYMODULE'),
  ('websockets.typing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.server',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.protocol',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.streams',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.frames',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.imports',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.http11',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('websockets.headers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.extensions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.legacy',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.uri',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.http',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.connection',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.cli',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.auth',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.__main__',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.client',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.auto',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.auto',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\auto.py',
   'PYMODULE'),
  ('uvicorn.lifespan.off',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\off.py',
   'PYMODULE'),
  ('uvicorn.lifespan',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\__init__.py',
   'PYMODULE'),
  ('uvicorn.lifespan.on',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\on.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_strptime.py',
   'PYMODULE'),
  ('uvicorn',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\__init__.py',
   'PYMODULE'),
  ('uvicorn.workers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\workers.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchgodreload',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\watchgodreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchfilesreload',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\watchfilesreload.py',
   'PYMODULE'),
  ('watchfiles',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\watchfiles\\__init__.py',
   'PYMODULE'),
  ('watchfiles.version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\watchfiles\\version.py',
   'PYMODULE'),
  ('watchfiles.run',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\watchfiles\\run.py',
   'PYMODULE'),
  ('watchfiles.main',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\watchfiles\\main.py',
   'PYMODULE'),
  ('watchfiles.filters',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\watchfiles\\filters.py',
   'PYMODULE'),
  ('uvicorn.supervisors.statreload',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\statreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.multiprocess',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\multiprocess.py',
   'PYMODULE'),
  ('uvicorn.supervisors.basereload',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\basereload.py',
   'PYMODULE'),
  ('uvicorn.supervisors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\__init__.py',
   'PYMODULE'),
  ('uvicorn._subprocess',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\_subprocess.py',
   'PYMODULE'),
  ('uvicorn.__main__',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\__main__.py',
   'PYMODULE'),
  ('uvicorn.main',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\main.py',
   'PYMODULE'),
  ('pydantic',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.tools',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.parse',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.json',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.generics',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('fastapi.middleware.cors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\middleware\\cors.py',
   'PYMODULE'),
  ('fastapi.middleware',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\middleware\\__init__.py',
   'PYMODULE'),
  ('starlette.middleware.cors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\middleware\\cors.py',
   'PYMODULE'),
  ('fastapi.staticfiles',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\staticfiles.py',
   'PYMODULE'),
  ('starlette.staticfiles',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\staticfiles.py',
   'PYMODULE'),
  ('fastapi.responses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\responses.py',
   'PYMODULE'),
  ('fastapi',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\__init__.py',
   'PYMODULE'),
  ('fastapi.websockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\websockets.py',
   'PYMODULE'),
  ('fastapi.requests',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\requests.py',
   'PYMODULE'),
  ('fastapi.applications',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\applications.py',
   'PYMODULE'),
  ('starlette.middleware.errors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\middleware\\errors.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE'),
  ('starlette.middleware.base',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\middleware\\base.py',
   'PYMODULE'),
  ('starlette.applications',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\applications.py',
   'PYMODULE'),
  ('fastapi.openapi.docs',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\openapi\\docs.py',
   'PYMODULE'),
  ('fastapi.middleware.asyncexitstack',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\middleware\\asyncexitstack.py',
   'PYMODULE'),
  ('fastapi.exception_handlers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\exception_handlers.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\typing.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pathlib.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\datetime.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tarfile.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\subprocess.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uuid.py',
   'PYMODULE')],
 [('python310.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python310.dll',
   'BINARY'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('pydantic_core\\_pydantic_core.cp310-win_amd64.pyd',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic_core\\_pydantic_core.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp310-win_amd64.pyd',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\_yaml.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('httptools\\parser\\url_parser.cp310-win_amd64.pyd',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\httptools\\parser\\url_parser.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('httptools\\parser\\parser.cp310-win_amd64.pyd',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\httptools\\parser\\parser.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('websockets\\speedups.cp310-win_amd64.pyd',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\speedups.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('watchfiles\\_rust_notify.cp310-win_amd64.pyd',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\watchfiles\\_rust_notify.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libssl-1_1.dll',
   'BINARY'),
  ('libffi-7.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\DLLs\\libffi-7.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY')],
 [],
 [],
 [('static\\index.html',
   'D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\static\\index.html',
   'DATA'),
  ('static\\script.js',
   'D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\static\\script.js',
   'DATA'),
  ('static\\styles.css',
   'D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\static\\styles.css',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('websockets-15.0.1.dist-info\\RECORD',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\WHEEL',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\WHEEL',
   'DATA'),
  ('websockets-15.0.1.dist-info\\top_level.txt',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\top_level.txt',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('websockets-15.0.1.dist-info\\LICENSE',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\LICENSE',
   'DATA'),
  ('websockets-15.0.1.dist-info\\INSTALLER',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\METADATA',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('websockets-15.0.1.dist-info\\entry_points.txt',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets-15.0.1.dist-info\\entry_points.txt',
   'DATA'),
  ('base_library.zip',
   'D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\build\\main\\base_library.zip',
   'DATA')],
 [('encodings.zlib_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\functools.py',
   'PYMODULE'),
  ('collections.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('enum',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\enum.py',
   'PYMODULE'),
  ('_collections_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\re.py',
   'PYMODULE'),
  ('abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\abc.py',
   'PYMODULE'),
  ('operator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\operator.py',
   'PYMODULE'),
  ('sre_parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sre_parse.py',
   'PYMODULE'),
  ('posixpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\posixpath.py',
   'PYMODULE'),
  ('stat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\stat.py',
   'PYMODULE'),
  ('io',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\io.py',
   'PYMODULE'),
  ('reprlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\reprlib.py',
   'PYMODULE'),
  ('_weakrefset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_weakrefset.py',
   'PYMODULE'),
  ('codecs',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\codecs.py',
   'PYMODULE'),
  ('linecache',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\linecache.py',
   'PYMODULE'),
  ('heapq',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\heapq.py',
   'PYMODULE'),
  ('os',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\os.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\genericpath.py',
   'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ntpath.py',
   'PYMODULE'),
  ('types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\types.py',
   'PYMODULE'),
  ('warnings',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\warnings.py',
   'PYMODULE'),
  ('traceback',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\traceback.py',
   'PYMODULE'),
  ('locale',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\locale.py',
   'PYMODULE'),
  ('keyword',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\keyword.py',
   'PYMODULE'),
  ('sre_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sre_compile.py',
   'PYMODULE'),
  ('copyreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\copyreg.py',
   'PYMODULE'),
  ('sre_constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\sre_constants.py',
   'PYMODULE'),
  ('weakref',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\weakref.py',
   'PYMODULE')])
