<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pandoc Web UI - 文档转换工具</title>
    <link rel="stylesheet" href="/static/styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-file-alt"></i>
                    <h1>Pandoc Web UI</h1>
                </div>
                <div class="header-info">
                    <span id="pandoc-version">Loading...</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Upload Section -->
            <section class="upload-section">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-cloud-upload-alt"></i> 文件上传</h2>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="upload-area">
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <p class="upload-text">拖拽文件到此处或点击选择文件</p>
                                <p class="upload-hint">支持多种格式：Markdown, HTML, DOCX, PDF, LaTeX 等</p>
                                <input type="file" id="file-input" class="file-input" accept=".md,.html,.docx,.pdf,.tex,.txt,.rtf,.odt,.epub">
                            </div>
                        </div>
                        
                        <!-- File Info -->
                        <div class="file-info" id="file-info" style="display: none;">
                            <div class="file-details">
                                <i class="fas fa-file"></i>
                                <div class="file-meta">
                                    <span class="file-name" id="file-name"></span>
                                    <span class="file-size" id="file-size"></span>
                                </div>
                                <button class="btn-remove" id="remove-file">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Conversion Settings -->
            <section class="conversion-section">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-cogs"></i> 转换设置</h2>
                    </div>
                    <div class="card-body">
                        <div class="format-selection">
                            <div class="format-group">
                                <label for="input-format">输入格式</label>
                                <select id="input-format" class="format-select">
                                    <option value="auto">自动检测</option>
                                </select>
                            </div>
                            <div class="format-arrow">
                                <i class="fas fa-arrow-right"></i>
                            </div>
                            <div class="format-group">
                                <label for="output-format">输出格式</label>
                                <select id="output-format" class="format-select">
                                    <option value="html">HTML</option>
                                </select>
                            </div>
                        </div>

                        <!-- Advanced Options -->
                        <div class="advanced-options">
                            <button class="btn-toggle" id="toggle-advanced">
                                <i class="fas fa-chevron-down"></i>
                                高级选项
                            </button>
                            <div class="options-panel" id="options-panel" style="display: none;">
                                <div class="option-group">
                                    <label>
                                        <input type="checkbox" id="standalone" checked>
                                        生成独立文档
                                    </label>
                                </div>
                                <div class="option-group">
                                    <label>
                                        <input type="checkbox" id="toc">
                                        包含目录
                                    </label>
                                </div>
                                <div class="option-group">
                                    <label>
                                        <input type="checkbox" id="number-sections">
                                        章节编号
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Convert Button -->
                        <button class="btn-convert" id="convert-btn" disabled>
                            <i class="fas fa-magic"></i>
                            开始转换
                        </button>
                    </div>
                </div>
            </section>

            <!-- Progress -->
            <section class="progress-section" id="progress-section" style="display: none;">
                <div class="card">
                    <div class="card-body">
                        <div class="progress-content">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progress-fill"></div>
                            </div>
                            <p class="progress-text" id="progress-text">正在转换...</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Result -->
            <section class="result-section" id="result-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-check-circle"></i> 转换结果</h2>
                    </div>
                    <div class="card-body">
                        <div class="result-content" id="result-content">
                            <!-- Result will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- History -->
            <section class="history-section">
                <div class="card">
                    <div class="card-header">
                        <h2><i class="fas fa-history"></i> 转换历史</h2>
                        <button class="btn-clear" id="clear-history">
                            <i class="fas fa-trash"></i>
                            清空历史
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="history-list" id="history-list">
                            <div class="empty-state">
                                <i class="fas fa-clock"></i>
                                <p>暂无转换记录</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Pandoc Web UI. Powered by <a href="https://pandoc.org" target="_blank">Pandoc</a> & <a href="https://fastapi.tiangolo.com" target="_blank">FastAPI</a></p>
        </div>
    </footer>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>处理中...</p>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container" id="toast-container"></div>

    <script src="/static/script.js"></script>
</body>
</html>
