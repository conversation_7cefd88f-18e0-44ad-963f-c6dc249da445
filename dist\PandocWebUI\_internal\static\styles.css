/* Pandoc Web UI - Modern CSS Styles */

/* CSS Variables for Theme */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 2rem;
    color: var(--primary-color);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Main Content */
.main {
    padding: 2rem 0;
    min-height: calc(100vh - 140px);
}

/* Cards */
.card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-secondary);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-header i {
    color: var(--primary-color);
}

.card-body {
    padding: 1.5rem;
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-lg);
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    transform: scale(1.02);
}

.upload-content {
    position: relative;
    z-index: 2;
}

.upload-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.upload-text {
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

/* File Info */
.file-info {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.file-details {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.file-details i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.file-meta {
    flex: 1;
}

.file-name {
    display: block;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.file-size {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.btn-remove {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.btn-remove:hover {
    background: rgba(239, 68, 68, 0.1);
}

/* Format Selection */
.format-selection {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 1rem;
    align-items: end;
    margin-bottom: 2rem;
}

.format-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.format-group label {
    font-weight: 500;
    color: var(--text-secondary);
}

.format-select {
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    font-size: 1rem;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
}

.format-select:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-secondary);
}

.format-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23667eea' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
}

/* 自定义下拉选项样式 */
.format-select option {
    padding: 0.75rem 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    border: none;
    font-size: 0.95rem;
    line-height: 1.5;
    transition: all 0.2s ease;
}

.format-select option:hover,
.format-select option:focus {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    color: var(--primary-color);
}

.format-select option:checked {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 500;
}

/* Firefox 特殊处理 */
@-moz-document url-prefix() {
    .format-select {
        background-image: none;
        padding-right: 1rem;
    }
}

/* Webkit 浏览器下拉选项美化 */
.format-select::-webkit-scrollbar {
    width: 8px;
}

.format-select::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
}

.format-select::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.format-select::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
}

/* 增强的下拉框容器样式 */
.format-group {
    position: relative;
}

.format-group::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: var(--radius-md);
    background: linear-gradient(135deg, transparent, rgba(102, 126, 234, 0.02));
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.format-group:hover::after {
    opacity: 1;
}

/* 下拉框打开时的动画效果 */
.format-select[aria-expanded="true"] {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

/* 选项分组样式（如果需要） */
.format-select optgroup {
    font-weight: 600;
    color: var(--text-secondary);
    background: var(--bg-tertiary);
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.format-select optgroup option {
    font-weight: 400;
    text-transform: none;
    letter-spacing: normal;
    padding-left: 1.5rem;
}

/* 自定义下拉框样式 */
.custom-select {
    position: relative;
    display: block;
    width: 100%;
}

.custom-select-trigger {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.custom-select-trigger:hover {
    border-color: var(--primary-color);
    background-color: var(--bg-secondary);
}

.custom-select-trigger.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.custom-select-arrow {
    width: 1rem;
    height: 1rem;
    transition: transform 0.2s ease;
    color: var(--text-secondary);
}

.custom-select-trigger.active .custom-select-arrow {
    transform: rotate(180deg);
    color: var(--primary-color);
}

.custom-select-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    box-shadow: var(--shadow-lg);
    max-height: 300px;
    overflow-y: auto;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.custom-select-options.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.custom-select-option {
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--border-light);
    font-size: 0.95rem;
    line-height: 1.4;
}

.custom-select-option:last-child {
    border-bottom: none;
}

.custom-select-option:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
    color: var(--primary-color);
    transform: translateX(2px);
}

.custom-select-option.selected {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    font-weight: 500;
}

.custom-select-option.selected:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
    transform: translateX(0);
}

/* 自定义滚动条 */
.custom-select-options::-webkit-scrollbar {
    width: 6px;
}

.custom-select-options::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

.custom-select-options::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
}

.custom-select-options::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
}

/* 搜索框样式（如果需要） */
.custom-select-search {
    padding: 0.5rem 1rem;
    border: none;
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-secondary);
    font-size: 0.9rem;
    outline: none;
    width: 100%;
}

.custom-select-search:focus {
    background: var(--bg-primary);
    border-bottom-color: var(--primary-color);
}

.custom-select-search::placeholder {
    color: var(--text-light);
    font-style: italic;
}

.format-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-size: 1.25rem;
    margin-top: 1.5rem;
}

/* Advanced Options */
.advanced-options {
    margin-bottom: 2rem;
}

.btn-toggle {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: all 0.2s ease;
}

.btn-toggle:hover {
    color: var(--primary-dark);
}

.btn-toggle i {
    transition: transform 0.2s ease;
}

.btn-toggle.active i {
    transform: rotate(180deg);
}

.options-panel {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-light);
}

.option-group {
    margin-bottom: 0.75rem;
}

.option-group:last-child {
    margin-bottom: 0;
}

.option-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 400;
}

.option-group input[type="checkbox"] {
    width: 1rem;
    height: 1rem;
    accent-color: var(--primary-color);
}

/* Buttons */
.btn-convert {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-convert:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-convert:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-clear {
    background: none;
    border: 1px solid var(--error-color);
    color: var(--error-color);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.btn-clear:hover {
    background: var(--error-color);
    color: white;
}

/* Progress */
.progress-content {
    text-align: center;
}

.progress-bar {
    width: 100%;
    height: 0.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-sm);
    transition: width 0.3s ease;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: 200px 0; }
}

.progress-text {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Result */
.result-success {
    padding: 1.5rem;
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: var(--radius-lg);
    text-align: center;
}

.result-error {
    padding: 1.5rem;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    border-radius: var(--radius-lg);
    text-align: center;
}

.btn-download {
    background: var(--success-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    margin-top: 1rem;
}

.btn-download:hover {
    background: #059669;
    transform: translateY(-1px);
}

/* History */
.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border: 1px solid var(--border-light);
    border-radius: var(--radius-md);
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.history-item:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

.history-info {
    flex: 1;
}

.history-filename {
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.history-meta {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.history-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-history {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-history:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-light);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-light);
    padding: 1rem 0;
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.footer a {
    color: var(--primary-color);
    text-decoration: none;
}

.footer a:hover {
    text-decoration: underline;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1100;
}

.toast {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    padding: 1rem 1.5rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 0.75rem;
    }

    .format-selection {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .format-arrow {
        transform: rotate(90deg);
        margin: 0;
    }

    .header-content {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .card-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .history-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .history-actions {
        align-self: stretch;
        justify-content: flex-end;
    }

    .toast {
        min-width: auto;
        max-width: calc(100vw - 2rem);
    }

    /* 移动端下拉框优化 */
    .format-select {
        font-size: 1rem;
        padding: 1rem;
        min-height: 48px; /* 触摸友好的最小高度 */
    }

    .custom-select-options {
        max-height: 250px;
        border-radius: var(--radius-lg);
        margin-top: 0.5rem;
        box-shadow: var(--shadow-xl);
    }

    .custom-select-option {
        padding: 1rem;
        font-size: 1rem;
        min-height: 48px;
        display: flex;
        align-items: center;
    }

    .custom-select-trigger {
        padding: 1rem;
        min-height: 48px;
    }

    .custom-select-search {
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }
}

/* 高分辨率屏幕优化 */
@media (min-resolution: 2dppx) {
    .format-select {
        background-size: 1.2rem;
    }
}

/* 深色模式支持（预留） */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-light: #9ca3af;
        --bg-primary: #1f2937;
        --bg-secondary: #374151;
        --bg-tertiary: #4b5563;
        --border-color: #4b5563;
        --border-light: #374151;
    }

    .format-select {
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23d1d5db' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    }

    .format-select:focus {
        background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23667eea' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    }
}
