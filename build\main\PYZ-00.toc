('D:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\build\\main\\PYZ-00.pyz',
 [('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\__future__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE'),
  ('annotated_types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\annotated_types\\__init__.py',
   'PYMODULE'),
  ('anyio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\__init__.py',
   'PYMODULE'),
  ('anyio._backends',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_backends\\__init__.py',
   'PYMODULE'),
  ('anyio._backends._asyncio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_backends\\_asyncio.py',
   'PYMODULE'),
  ('anyio._backends._trio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_backends\\_trio.py',
   'PYMODULE'),
  ('anyio._core',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\__init__.py',
   'PYMODULE'),
  ('anyio._core._compat',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_compat.py',
   'PYMODULE'),
  ('anyio._core._eventloop',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_eventloop.py',
   'PYMODULE'),
  ('anyio._core._exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_exceptions.py',
   'PYMODULE'),
  ('anyio._core._fileio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_fileio.py',
   'PYMODULE'),
  ('anyio._core._resources',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_resources.py',
   'PYMODULE'),
  ('anyio._core._signals',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_signals.py',
   'PYMODULE'),
  ('anyio._core._sockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_sockets.py',
   'PYMODULE'),
  ('anyio._core._streams',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_streams.py',
   'PYMODULE'),
  ('anyio._core._subprocesses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_subprocesses.py',
   'PYMODULE'),
  ('anyio._core._synchronization',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_synchronization.py',
   'PYMODULE'),
  ('anyio._core._tasks',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_tasks.py',
   'PYMODULE'),
  ('anyio._core._testing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_testing.py',
   'PYMODULE'),
  ('anyio._core._typedattr',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\_core\\_typedattr.py',
   'PYMODULE'),
  ('anyio.abc',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\__init__.py',
   'PYMODULE'),
  ('anyio.abc._resources',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_resources.py',
   'PYMODULE'),
  ('anyio.abc._sockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_sockets.py',
   'PYMODULE'),
  ('anyio.abc._streams',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_streams.py',
   'PYMODULE'),
  ('anyio.abc._subprocesses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_subprocesses.py',
   'PYMODULE'),
  ('anyio.abc._tasks',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_tasks.py',
   'PYMODULE'),
  ('anyio.abc._testing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\abc\\_testing.py',
   'PYMODULE'),
  ('anyio.from_thread',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\from_thread.py',
   'PYMODULE'),
  ('anyio.lowlevel',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\lowlevel.py',
   'PYMODULE'),
  ('anyio.streams',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\streams\\__init__.py',
   'PYMODULE'),
  ('anyio.streams.memory',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\streams\\memory.py',
   'PYMODULE'),
  ('anyio.streams.stapled',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\streams\\stapled.py',
   'PYMODULE'),
  ('anyio.streams.tls',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\streams\\tls.py',
   'PYMODULE'),
  ('anyio.to_thread',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\anyio\\to_thread.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\argparse.py',
   'PYMODULE'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ast.py',
   'PYMODULE'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bisect.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\bz2.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\calendar.py',
   'PYMODULE'),
  ('click',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\__init__.py',
   'PYMODULE'),
  ('click._compat',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\_compat.py',
   'PYMODULE'),
  ('click._termui_impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\_termui_impl.py',
   'PYMODULE'),
  ('click._textwrap',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\_textwrap.py',
   'PYMODULE'),
  ('click._winconsole',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\_winconsole.py',
   'PYMODULE'),
  ('click.core',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\core.py',
   'PYMODULE'),
  ('click.decorators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\decorators.py',
   'PYMODULE'),
  ('click.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\exceptions.py',
   'PYMODULE'),
  ('click.formatting',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\formatting.py',
   'PYMODULE'),
  ('click.globals',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\globals.py',
   'PYMODULE'),
  ('click.parser',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\parser.py',
   'PYMODULE'),
  ('click.shell_completion',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\shell_completion.py',
   'PYMODULE'),
  ('click.termui',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\termui.py',
   'PYMODULE'),
  ('click.types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\types.py',
   'PYMODULE'),
  ('click.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\click\\utils.py',
   'PYMODULE'),
  ('colorama',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\__init__.py',
   'PYMODULE'),
  ('colorama.ansi',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\ansi.py',
   'PYMODULE'),
  ('colorama.ansitowin32',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\ansitowin32.py',
   'PYMODULE'),
  ('colorama.initialise',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\initialise.py',
   'PYMODULE'),
  ('colorama.win32',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\win32.py',
   'PYMODULE'),
  ('colorama.winterm',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\colorama\\winterm.py',
   'PYMODULE'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\colorsys.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\contextvars.py',
   'PYMODULE'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\copy.py',
   'PYMODULE'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\csv.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\decimal.py',
   'PYMODULE'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\difflib.py',
   'PYMODULE'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\dis.py',
   'PYMODULE'),
  ('dotenv',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\dotenv\\__init__.py',
   'PYMODULE'),
  ('dotenv.ipython',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\dotenv\\ipython.py',
   'PYMODULE'),
  ('dotenv.main',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\dotenv\\main.py',
   'PYMODULE'),
  ('dotenv.parser',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\dotenv\\parser.py',
   'PYMODULE'),
  ('dotenv.variables',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\dotenv\\variables.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\mime\\__init__.py',
   'PYMODULE'),
  ('email.mime.base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\mime\\base.py',
   'PYMODULE'),
  ('email.mime.multipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\mime\\multipart.py',
   'PYMODULE'),
  ('email.mime.nonmultipart',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\mime\\nonmultipart.py',
   'PYMODULE'),
  ('email.mime.text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\mime\\text.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\email\\utils.py',
   'PYMODULE'),
  ('fastapi',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\__init__.py',
   'PYMODULE'),
  ('fastapi._compat',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\_compat.py',
   'PYMODULE'),
  ('fastapi.applications',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\applications.py',
   'PYMODULE'),
  ('fastapi.background',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\background.py',
   'PYMODULE'),
  ('fastapi.concurrency',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\concurrency.py',
   'PYMODULE'),
  ('fastapi.datastructures',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\datastructures.py',
   'PYMODULE'),
  ('fastapi.dependencies',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\dependencies\\__init__.py',
   'PYMODULE'),
  ('fastapi.dependencies.models',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\dependencies\\models.py',
   'PYMODULE'),
  ('fastapi.dependencies.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\dependencies\\utils.py',
   'PYMODULE'),
  ('fastapi.encoders',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\encoders.py',
   'PYMODULE'),
  ('fastapi.exception_handlers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\exception_handlers.py',
   'PYMODULE'),
  ('fastapi.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\exceptions.py',
   'PYMODULE'),
  ('fastapi.logger',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\logger.py',
   'PYMODULE'),
  ('fastapi.middleware',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\middleware\\__init__.py',
   'PYMODULE'),
  ('fastapi.middleware.asyncexitstack',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\middleware\\asyncexitstack.py',
   'PYMODULE'),
  ('fastapi.middleware.cors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\middleware\\cors.py',
   'PYMODULE'),
  ('fastapi.openapi',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\openapi\\__init__.py',
   'PYMODULE'),
  ('fastapi.openapi.constants',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\openapi\\constants.py',
   'PYMODULE'),
  ('fastapi.openapi.docs',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\openapi\\docs.py',
   'PYMODULE'),
  ('fastapi.openapi.models',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\openapi\\models.py',
   'PYMODULE'),
  ('fastapi.openapi.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\openapi\\utils.py',
   'PYMODULE'),
  ('fastapi.param_functions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\param_functions.py',
   'PYMODULE'),
  ('fastapi.params',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\params.py',
   'PYMODULE'),
  ('fastapi.requests',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\requests.py',
   'PYMODULE'),
  ('fastapi.responses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\responses.py',
   'PYMODULE'),
  ('fastapi.routing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\routing.py',
   'PYMODULE'),
  ('fastapi.security',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\__init__.py',
   'PYMODULE'),
  ('fastapi.security.api_key',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\api_key.py',
   'PYMODULE'),
  ('fastapi.security.base',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\base.py',
   'PYMODULE'),
  ('fastapi.security.http',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\http.py',
   'PYMODULE'),
  ('fastapi.security.oauth2',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\oauth2.py',
   'PYMODULE'),
  ('fastapi.security.open_id_connect_url',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\open_id_connect_url.py',
   'PYMODULE'),
  ('fastapi.security.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\security\\utils.py',
   'PYMODULE'),
  ('fastapi.staticfiles',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\staticfiles.py',
   'PYMODULE'),
  ('fastapi.types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\types.py',
   'PYMODULE'),
  ('fastapi.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\utils.py',
   'PYMODULE'),
  ('fastapi.websockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\fastapi\\websockets.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gettext.py',
   'PYMODULE'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\glob.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\gzip.py',
   'PYMODULE'),
  ('h11',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\__init__.py',
   'PYMODULE'),
  ('h11._abnf',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_abnf.py',
   'PYMODULE'),
  ('h11._connection',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_connection.py',
   'PYMODULE'),
  ('h11._events',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_events.py',
   'PYMODULE'),
  ('h11._headers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_headers.py',
   'PYMODULE'),
  ('h11._readers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_readers.py',
   'PYMODULE'),
  ('h11._receivebuffer',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_receivebuffer.py',
   'PYMODULE'),
  ('h11._state',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_state.py',
   'PYMODULE'),
  ('h11._util',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_util.py',
   'PYMODULE'),
  ('h11._version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_version.py',
   'PYMODULE'),
  ('h11._writers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\h11\\_writers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hashlib.py',
   'PYMODULE'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\hmac.py',
   'PYMODULE'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\http\\cookies.py',
   'PYMODULE'),
  ('httptools',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\httptools\\__init__.py',
   'PYMODULE'),
  ('httptools._version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\httptools\\_version.py',
   'PYMODULE'),
  ('httptools.parser',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\httptools\\parser\\__init__.py',
   'PYMODULE'),
  ('httptools.parser.errors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\httptools\\parser\\errors.py',
   'PYMODULE'),
  ('idna',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.core',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.idnadata',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('idna.intranges',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.package_data',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.uts46data',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('logging.config',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\config.py',
   'PYMODULE'),
  ('logging.handlers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\logging\\handlers.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\lzma.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\mimetypes.py',
   'PYMODULE'),
  ('multipart',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\multipart\\__init__.py',
   'PYMODULE'),
  ('multipart.decoders',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\multipart\\decoders.py',
   'PYMODULE'),
  ('multipart.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\multipart\\exceptions.py',
   'PYMODULE'),
  ('multipart.multipart',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\multipart\\multipart.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\opcode.py',
   'PYMODULE'),
  ('optparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\optparse.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pathlib.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pkgutil.py',
   'PYMODULE'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\platform.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\pprint.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\py_compile.py',
   'PYMODULE'),
  ('pydantic',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\__init__.py',
   'PYMODULE'),
  ('pydantic._internal._config',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_config.py',
   'PYMODULE'),
  ('pydantic._internal._core_metadata',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_core_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._core_utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_core_utils.py',
   'PYMODULE'),
  ('pydantic._internal._dataclasses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_dataclasses.py',
   'PYMODULE'),
  ('pydantic._internal._decorators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_decorators.py',
   'PYMODULE'),
  ('pydantic._internal._decorators_v1',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_decorators_v1.py',
   'PYMODULE'),
  ('pydantic._internal._discriminated_union',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_discriminated_union.py',
   'PYMODULE'),
  ('pydantic._internal._fields',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_fields.py',
   'PYMODULE'),
  ('pydantic._internal._forward_ref',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_forward_ref.py',
   'PYMODULE'),
  ('pydantic._internal._generate_schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_generate_schema.py',
   'PYMODULE'),
  ('pydantic._internal._generics',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_generics.py',
   'PYMODULE'),
  ('pydantic._internal._internal_dataclass',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_internal_dataclass.py',
   'PYMODULE'),
  ('pydantic._internal._known_annotated_metadata',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py',
   'PYMODULE'),
  ('pydantic._internal._mock_val_ser',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_mock_val_ser.py',
   'PYMODULE'),
  ('pydantic._internal._model_construction',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_model_construction.py',
   'PYMODULE'),
  ('pydantic._internal._repr',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_repr.py',
   'PYMODULE'),
  ('pydantic._internal._schema_generation_shared',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_schema_generation_shared.py',
   'PYMODULE'),
  ('pydantic._internal._std_types_schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_std_types_schema.py',
   'PYMODULE'),
  ('pydantic._internal._typing_extra',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_typing_extra.py',
   'PYMODULE'),
  ('pydantic._internal._utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_utils.py',
   'PYMODULE'),
  ('pydantic._internal._validate_call',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_validate_call.py',
   'PYMODULE'),
  ('pydantic._internal._validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_internal\\_validators.py',
   'PYMODULE'),
  ('pydantic._migration',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\_migration.py',
   'PYMODULE'),
  ('pydantic.alias_generators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\alias_generators.py',
   'PYMODULE'),
  ('pydantic.annotated_handlers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\annotated_handlers.py',
   'PYMODULE'),
  ('pydantic.class_validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\class_validators.py',
   'PYMODULE'),
  ('pydantic.color',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\color.py',
   'PYMODULE'),
  ('pydantic.config',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\config.py',
   'PYMODULE'),
  ('pydantic.dataclasses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.datetime_parse',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.decorator',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\__init__.py',
   'PYMODULE'),
  ('pydantic.deprecated.class_validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\class_validators.py',
   'PYMODULE'),
  ('pydantic.deprecated.config',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\config.py',
   'PYMODULE'),
  ('pydantic.deprecated.copy_internals',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\copy_internals.py',
   'PYMODULE'),
  ('pydantic.deprecated.decorator',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\decorator.py',
   'PYMODULE'),
  ('pydantic.deprecated.json',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\json.py',
   'PYMODULE'),
  ('pydantic.deprecated.parse',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\parse.py',
   'PYMODULE'),
  ('pydantic.deprecated.tools',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\deprecated\\tools.py',
   'PYMODULE'),
  ('pydantic.env_settings',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\env_settings.py',
   'PYMODULE'),
  ('pydantic.error_wrappers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.errors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\errors.py',
   'PYMODULE'),
  ('pydantic.fields',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\fields.py',
   'PYMODULE'),
  ('pydantic.functional_serializers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\functional_serializers.py',
   'PYMODULE'),
  ('pydantic.functional_validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\functional_validators.py',
   'PYMODULE'),
  ('pydantic.generics',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\generics.py',
   'PYMODULE'),
  ('pydantic.json',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\json.py',
   'PYMODULE'),
  ('pydantic.json_schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\json_schema.py',
   'PYMODULE'),
  ('pydantic.main',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\main.py',
   'PYMODULE'),
  ('pydantic.mypy',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\mypy.py',
   'PYMODULE'),
  ('pydantic.networks',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\networks.py',
   'PYMODULE'),
  ('pydantic.parse',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\parse.py',
   'PYMODULE'),
  ('pydantic.plugin',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\plugin\\__init__.py',
   'PYMODULE'),
  ('pydantic.plugin._loader',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\plugin\\_loader.py',
   'PYMODULE'),
  ('pydantic.plugin._schema_validator',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\plugin\\_schema_validator.py',
   'PYMODULE'),
  ('pydantic.root_model',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\root_model.py',
   'PYMODULE'),
  ('pydantic.schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\schema.py',
   'PYMODULE'),
  ('pydantic.tools',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\tools.py',
   'PYMODULE'),
  ('pydantic.type_adapter',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\type_adapter.py',
   'PYMODULE'),
  ('pydantic.types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\types.py',
   'PYMODULE'),
  ('pydantic.typing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\typing.py',
   'PYMODULE'),
  ('pydantic.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\utils.py',
   'PYMODULE'),
  ('pydantic.v1',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\__init__.py',
   'PYMODULE'),
  ('pydantic.v1._hypothesis_plugin',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\_hypothesis_plugin.py',
   'PYMODULE'),
  ('pydantic.v1.annotated_types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\annotated_types.py',
   'PYMODULE'),
  ('pydantic.v1.class_validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\class_validators.py',
   'PYMODULE'),
  ('pydantic.v1.color',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\color.py',
   'PYMODULE'),
  ('pydantic.v1.config',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\config.py',
   'PYMODULE'),
  ('pydantic.v1.dataclasses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\dataclasses.py',
   'PYMODULE'),
  ('pydantic.v1.datetime_parse',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\datetime_parse.py',
   'PYMODULE'),
  ('pydantic.v1.decorator',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\decorator.py',
   'PYMODULE'),
  ('pydantic.v1.env_settings',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\env_settings.py',
   'PYMODULE'),
  ('pydantic.v1.error_wrappers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\error_wrappers.py',
   'PYMODULE'),
  ('pydantic.v1.errors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\errors.py',
   'PYMODULE'),
  ('pydantic.v1.fields',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\fields.py',
   'PYMODULE'),
  ('pydantic.v1.generics',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\generics.py',
   'PYMODULE'),
  ('pydantic.v1.json',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\json.py',
   'PYMODULE'),
  ('pydantic.v1.main',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\main.py',
   'PYMODULE'),
  ('pydantic.v1.mypy',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\mypy.py',
   'PYMODULE'),
  ('pydantic.v1.networks',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\networks.py',
   'PYMODULE'),
  ('pydantic.v1.parse',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\parse.py',
   'PYMODULE'),
  ('pydantic.v1.schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\schema.py',
   'PYMODULE'),
  ('pydantic.v1.tools',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\tools.py',
   'PYMODULE'),
  ('pydantic.v1.types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\types.py',
   'PYMODULE'),
  ('pydantic.v1.typing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\typing.py',
   'PYMODULE'),
  ('pydantic.v1.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\utils.py',
   'PYMODULE'),
  ('pydantic.v1.validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\validators.py',
   'PYMODULE'),
  ('pydantic.v1.version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\v1\\version.py',
   'PYMODULE'),
  ('pydantic.validate_call_decorator',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\validate_call_decorator.py',
   'PYMODULE'),
  ('pydantic.validators',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\validators.py',
   'PYMODULE'),
  ('pydantic.version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\version.py',
   'PYMODULE'),
  ('pydantic.warnings',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic\\warnings.py',
   'PYMODULE'),
  ('pydantic_core',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic_core\\__init__.py',
   'PYMODULE'),
  ('pydantic_core.core_schema',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\pydantic_core\\core_schema.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\random.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\runpy.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\selectors.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\signal.py',
   'PYMODULE'),
  ('smtplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\smtplib.py',
   'PYMODULE'),
  ('sniffio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\sniffio\\__init__.py',
   'PYMODULE'),
  ('sniffio._impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\sniffio\\_impl.py',
   'PYMODULE'),
  ('sniffio._version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\sniffio\\_version.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socket.py',
   'PYMODULE'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\socketserver.py',
   'PYMODULE'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\ssl.py',
   'PYMODULE'),
  ('starlette',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\__init__.py',
   'PYMODULE'),
  ('starlette._compat',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\_compat.py',
   'PYMODULE'),
  ('starlette._utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\_utils.py',
   'PYMODULE'),
  ('starlette.applications',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\applications.py',
   'PYMODULE'),
  ('starlette.background',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\background.py',
   'PYMODULE'),
  ('starlette.concurrency',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\concurrency.py',
   'PYMODULE'),
  ('starlette.convertors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\convertors.py',
   'PYMODULE'),
  ('starlette.datastructures',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\datastructures.py',
   'PYMODULE'),
  ('starlette.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\exceptions.py',
   'PYMODULE'),
  ('starlette.formparsers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\formparsers.py',
   'PYMODULE'),
  ('starlette.middleware',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\middleware\\__init__.py',
   'PYMODULE'),
  ('starlette.middleware.base',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\middleware\\base.py',
   'PYMODULE'),
  ('starlette.middleware.cors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\middleware\\cors.py',
   'PYMODULE'),
  ('starlette.middleware.errors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\middleware\\errors.py',
   'PYMODULE'),
  ('starlette.middleware.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\middleware\\exceptions.py',
   'PYMODULE'),
  ('starlette.requests',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\requests.py',
   'PYMODULE'),
  ('starlette.responses',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\responses.py',
   'PYMODULE'),
  ('starlette.routing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\routing.py',
   'PYMODULE'),
  ('starlette.staticfiles',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\staticfiles.py',
   'PYMODULE'),
  ('starlette.status',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\status.py',
   'PYMODULE'),
  ('starlette.types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\types.py',
   'PYMODULE'),
  ('starlette.websockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\starlette\\websockets.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\tty.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\typing.py',
   'PYMODULE'),
  ('typing_extensions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uu.py',
   'PYMODULE'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\uuid.py',
   'PYMODULE'),
  ('uvicorn',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\__init__.py',
   'PYMODULE'),
  ('uvicorn.__main__',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\__main__.py',
   'PYMODULE'),
  ('uvicorn._subprocess',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\_subprocess.py',
   'PYMODULE'),
  ('uvicorn._types',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\_types.py',
   'PYMODULE'),
  ('uvicorn.config',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\config.py',
   'PYMODULE'),
  ('uvicorn.importer',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\importer.py',
   'PYMODULE'),
  ('uvicorn.lifespan',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\__init__.py',
   'PYMODULE'),
  ('uvicorn.lifespan.off',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\off.py',
   'PYMODULE'),
  ('uvicorn.lifespan.on',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\lifespan\\on.py',
   'PYMODULE'),
  ('uvicorn.logging',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\logging.py',
   'PYMODULE'),
  ('uvicorn.loops',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\loops\\__init__.py',
   'PYMODULE'),
  ('uvicorn.loops.asyncio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\loops\\asyncio.py',
   'PYMODULE'),
  ('uvicorn.loops.auto',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\loops\\auto.py',
   'PYMODULE'),
  ('uvicorn.loops.uvloop',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\loops\\uvloop.py',
   'PYMODULE'),
  ('uvicorn.main',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\main.py',
   'PYMODULE'),
  ('uvicorn.middleware',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\middleware\\__init__.py',
   'PYMODULE'),
  ('uvicorn.middleware.asgi2',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\middleware\\asgi2.py',
   'PYMODULE'),
  ('uvicorn.middleware.message_logger',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\middleware\\message_logger.py',
   'PYMODULE'),
  ('uvicorn.middleware.proxy_headers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\middleware\\proxy_headers.py',
   'PYMODULE'),
  ('uvicorn.middleware.wsgi',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\middleware\\wsgi.py',
   'PYMODULE'),
  ('uvicorn.protocols',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.http',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.auto',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.flow_control',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\flow_control.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.h11_impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\h11_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.http.httptools_impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\http\\httptools_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\utils.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\__init__.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.auto',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\auto.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.websockets_impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\websockets_impl.py',
   'PYMODULE'),
  ('uvicorn.protocols.websockets.wsproto_impl',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\protocols\\websockets\\wsproto_impl.py',
   'PYMODULE'),
  ('uvicorn.server',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\server.py',
   'PYMODULE'),
  ('uvicorn.supervisors',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\__init__.py',
   'PYMODULE'),
  ('uvicorn.supervisors.basereload',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\basereload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.multiprocess',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\multiprocess.py',
   'PYMODULE'),
  ('uvicorn.supervisors.statreload',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\statreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchfilesreload',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\watchfilesreload.py',
   'PYMODULE'),
  ('uvicorn.supervisors.watchgodreload',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\supervisors\\watchgodreload.py',
   'PYMODULE'),
  ('uvicorn.workers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\uvicorn\\workers.py',
   'PYMODULE'),
  ('watchfiles',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\watchfiles\\__init__.py',
   'PYMODULE'),
  ('watchfiles.filters',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\watchfiles\\filters.py',
   'PYMODULE'),
  ('watchfiles.main',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\watchfiles\\main.py',
   'PYMODULE'),
  ('watchfiles.run',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\watchfiles\\run.py',
   'PYMODULE'),
  ('watchfiles.version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\watchfiles\\version.py',
   'PYMODULE'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\webbrowser.py',
   'PYMODULE'),
  ('websockets',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\__init__.py',
   'PYMODULE'),
  ('websockets.__main__',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\__main__.py',
   'PYMODULE'),
  ('websockets.asyncio',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\__init__.py',
   'PYMODULE'),
  ('websockets.asyncio.async_timeout',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\async_timeout.py',
   'PYMODULE'),
  ('websockets.asyncio.client',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\client.py',
   'PYMODULE'),
  ('websockets.asyncio.compatibility',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\compatibility.py',
   'PYMODULE'),
  ('websockets.asyncio.connection',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\connection.py',
   'PYMODULE'),
  ('websockets.asyncio.messages',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\messages.py',
   'PYMODULE'),
  ('websockets.asyncio.router',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\router.py',
   'PYMODULE'),
  ('websockets.asyncio.server',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\asyncio\\server.py',
   'PYMODULE'),
  ('websockets.auth',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\auth.py',
   'PYMODULE'),
  ('websockets.cli',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\cli.py',
   'PYMODULE'),
  ('websockets.client',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\client.py',
   'PYMODULE'),
  ('websockets.connection',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\connection.py',
   'PYMODULE'),
  ('websockets.datastructures',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\datastructures.py',
   'PYMODULE'),
  ('websockets.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\exceptions.py',
   'PYMODULE'),
  ('websockets.extensions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\extensions\\__init__.py',
   'PYMODULE'),
  ('websockets.extensions.base',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\extensions\\base.py',
   'PYMODULE'),
  ('websockets.extensions.permessage_deflate',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\extensions\\permessage_deflate.py',
   'PYMODULE'),
  ('websockets.frames',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\frames.py',
   'PYMODULE'),
  ('websockets.headers',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\headers.py',
   'PYMODULE'),
  ('websockets.http',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\http.py',
   'PYMODULE'),
  ('websockets.http11',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\http11.py',
   'PYMODULE'),
  ('websockets.imports',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\imports.py',
   'PYMODULE'),
  ('websockets.legacy',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\__init__.py',
   'PYMODULE'),
  ('websockets.legacy.auth',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\auth.py',
   'PYMODULE'),
  ('websockets.legacy.client',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\client.py',
   'PYMODULE'),
  ('websockets.legacy.exceptions',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\exceptions.py',
   'PYMODULE'),
  ('websockets.legacy.framing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\framing.py',
   'PYMODULE'),
  ('websockets.legacy.handshake',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\handshake.py',
   'PYMODULE'),
  ('websockets.legacy.http',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\http.py',
   'PYMODULE'),
  ('websockets.legacy.protocol',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\protocol.py',
   'PYMODULE'),
  ('websockets.legacy.server',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\legacy\\server.py',
   'PYMODULE'),
  ('websockets.protocol',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\protocol.py',
   'PYMODULE'),
  ('websockets.server',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\server.py',
   'PYMODULE'),
  ('websockets.streams',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\streams.py',
   'PYMODULE'),
  ('websockets.sync',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\__init__.py',
   'PYMODULE'),
  ('websockets.sync.client',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\client.py',
   'PYMODULE'),
  ('websockets.sync.connection',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\connection.py',
   'PYMODULE'),
  ('websockets.sync.messages',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\messages.py',
   'PYMODULE'),
  ('websockets.sync.router',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\router.py',
   'PYMODULE'),
  ('websockets.sync.server',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\server.py',
   'PYMODULE'),
  ('websockets.sync.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\sync\\utils.py',
   'PYMODULE'),
  ('websockets.typing',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\typing.py',
   'PYMODULE'),
  ('websockets.uri',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\uri.py',
   'PYMODULE'),
  ('websockets.utils',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\utils.py',
   'PYMODULE'),
  ('websockets.version',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\websockets\\version.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'd:\\Archives\\Documents\\CodeSpace\\PandocWebUI\\.venv\\lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\lib\\zipimport.py',
   'PYMODULE')])
