#!/usr/bin/env python3
"""
Pandoc Web UI - FastAPI Backend
A beautiful and interactive web interface for Pandoc document conversion
"""

import uuid
import subprocess
import shutil
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
import json

from fastapi import Fast<PERSON>I, File, UploadFile, HTTPException, Form
from fastapi.responses import FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn

# Configuration
UPLOAD_DIR = Path("uploads")
OUTPUT_DIR = Path("outputs")
STATIC_DIR = Path("static")
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
MAX_HISTORY_ITEMS = 20

# Create directories
for directory in [UPLOAD_DIR, OUTPUT_DIR, STATIC_DIR]:
    directory.mkdir(exist_ok=True)

# FastAPI app
app = FastAPI(
    title="Pandoc Web UI",
    description="A beautiful web interface for Pandoc document conversion",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

# Data models
class ConversionRequest(BaseModel):
    input_format: str
    output_format: str
    options: Optional[Dict[str, Any]] = {}

class ConversionHistory(BaseModel):
    id: str
    filename: str
    input_format: str
    output_format: str
    timestamp: datetime
    status: str
    output_filename: Optional[str] = None
    error_message: Optional[str] = None

# In-memory storage for conversion history
conversion_history: List[ConversionHistory] = []

# Supported formats (based on Pandoc documentation)
SUPPORTED_INPUT_FORMATS = [
    "bibtex", "biblatex", "bits", "commonmark", "commonmark_x", "creole",
    "csljson", "csv", "tsv", "djot", "docbook", "docx", "dokuwiki",
    "endnotexml", "epub", "fb2", "gfm", "haddock", "html", "ipynb",
    "jats", "jira", "json", "latex", "markdown", "markdown_mmd",
    "markdown_phpextra", "markdown_strict", "mediawiki", "man", "mdoc",
    "muse", "native", "odt", "opml", "org", "pod", "ris", "rtf", "rst",
    "t2t", "textile", "tikiwiki", "twiki", "typst", "vimwiki"
]

SUPPORTED_OUTPUT_FORMATS = [
    "ansi", "asciidoc", "asciidoc_legacy", "asciidoctor", "beamer",
    "bibtex", "biblatex", "chunkedhtml", "commonmark", "commonmark_x",
    "context", "csljson", "djot", "docbook", "docbook4", "docbook5",
    "docx", "dokuwiki", "epub", "epub2", "epub3", "fb2", "gfm",
    "haddock", "html", "html4", "html5", "icml", "ipynb", "jats",
    "jats_archiving", "jats_articleauthoring", "jats_publishing",
    "jira", "json", "latex", "man", "markdown", "markdown_mmd",
    "markdown_phpextra", "markdown_strict", "markua", "mediawiki",
    "ms", "muse", "native", "odt", "opml", "opendocument", "org",
    "pdf", "plain", "pptx", "rst", "rtf", "texinfo", "textile",
    "slideous", "slidy", "dzslides", "revealjs", "s5", "tei",
    "typst", "xwiki", "zimwiki"
]

def get_pandoc_version():
    """Get Pandoc version"""
    try:
        result = subprocess.run(["pandoc", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout.split('\n')[0]
        return "Pandoc not found"
    except Exception:
        return "Pandoc not found"

def cleanup_old_files():
    """Clean up old files to save disk space"""
    try:
        # Remove files older than 1 hour
        import time
        current_time = time.time()
        
        for directory in [UPLOAD_DIR, OUTPUT_DIR]:
            for file_path in directory.glob("*"):
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > 3600:  # 1 hour
                        file_path.unlink()
    except Exception as e:
        print(f"Cleanup error: {e}")

def convert_document(input_file: Path, output_file: Path, input_format: str, output_format: str, options: Dict[str, Any] = None) -> tuple[bool, str]:
    """Convert document using Pandoc"""
    try:
        # Build pandoc command
        cmd = ["pandoc"]
        
        # Input format
        if input_format != "auto":
            cmd.extend(["-f", input_format])
        
        # Output format
        cmd.extend(["-t", output_format])
        
        # Add common options
        if output_format in ["html", "html5"]:
            cmd.extend(["--standalone", "--self-contained"])
        elif output_format == "pdf":
            cmd.extend(["--pdf-engine=xelatex"])
        
        # Add custom options
        if options:
            for key, value in options.items():
                if value is True:
                    cmd.append(f"--{key}")
                elif value and value is not False:
                    cmd.extend([f"--{key}", str(value)])
        
        # Input and output files
        cmd.extend([str(input_file), "-o", str(output_file)])
        
        # Execute pandoc
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            return True, "Conversion successful"
        else:
            return False, result.stderr or "Unknown error"
            
    except subprocess.TimeoutExpired:
        return False, "Conversion timeout"
    except Exception as e:
        return False, str(e)

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """Serve the main HTML page"""
    html_file = STATIC_DIR / "index.html"
    if html_file.exists():
        return HTMLResponse(content=html_file.read_text(encoding='utf-8'), status_code=200)
    else:
        return HTMLResponse(content="""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Pandoc Web UI</title>
        </head>
        <body>
            <h1>Pandoc Web UI</h1>
            <p>Please create the static/index.html file.</p>
        </body>
        </html>
        """, status_code=200)

@app.get("/api/info")
async def get_info():
    """Get application information"""
    return {
        "name": "Pandoc Web UI",
        "version": "1.0.0",
        "pandoc_version": get_pandoc_version(),
        "supported_input_formats": SUPPORTED_INPUT_FORMATS,
        "supported_output_formats": SUPPORTED_OUTPUT_FORMATS
    }

@app.get("/api/formats")
async def get_formats():
    """Get supported formats"""
    return {
        "input_formats": SUPPORTED_INPUT_FORMATS,
        "output_formats": SUPPORTED_OUTPUT_FORMATS
    }

@app.post("/api/upload")
async def upload_file(file: UploadFile = File(...)):
    """Upload a file for conversion"""
    try:
        # Check file size
        if file.size and file.size > MAX_FILE_SIZE:
            raise HTTPException(status_code=413, detail="File too large")
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        file_extension = Path(file.filename).suffix if file.filename else ""
        upload_filename = f"{file_id}{file_extension}"
        upload_path = UPLOAD_DIR / upload_filename
        
        # Save file
        with open(upload_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        return {
            "file_id": file_id,
            "filename": file.filename,
            "upload_filename": upload_filename,
            "size": upload_path.stat().st_size
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/convert")
async def convert_file(
    file_id: str = Form(...),
    input_format: str = Form(...),
    output_format: str = Form(...),
    options: str = Form("{}")
):
    """Convert uploaded file"""
    try:
        # Parse options
        try:
            conversion_options = json.loads(options)
        except json.JSONDecodeError:
            conversion_options = {}
        
        # Find input file
        input_files = list(UPLOAD_DIR.glob(f"{file_id}*"))
        if not input_files:
            raise HTTPException(status_code=404, detail="File not found")
        
        input_file = input_files[0]
        original_filename = input_file.name
        
        # Generate output filename
        output_extension = {
            # HTML variants
            "html": ".html", "html4": ".html", "html5": ".html",
            "chunkedhtml": ".zip",

            # Markdown variants
            "markdown": ".md", "commonmark": ".md", "commonmark_x": ".md",
            "gfm": ".md", "markdown_mmd": ".md", "markdown_phpextra": ".md",
            "markdown_strict": ".md", "markua": ".md",

            # Office documents
            "docx": ".docx", "odt": ".odt", "pptx": ".pptx",
            "opendocument": ".xml",

            # E-books
            "epub": ".epub", "epub2": ".epub", "epub3": ".epub",
            "fb2": ".fb2",

            # LaTeX and academic
            "latex": ".tex", "beamer": ".tex", "context": ".tex",

            # PDF
            "pdf": ".pdf",

            # Text formats
            "plain": ".txt", "ansi": ".txt",

            # Markup languages
            "rst": ".rst", "textile": ".textile", "asciidoc": ".adoc",
            "asciidoc_legacy": ".adoc", "asciidoctor": ".adoc",
            "mediawiki": ".wiki", "dokuwiki": ".txt", "tikiwiki": ".txt",
            "xwiki": ".txt", "zimwiki": ".txt", "vimwiki": ".txt",
            "org": ".org", "muse": ".muse", "djot": ".dj",

            # Presentation formats
            "slideous": ".html", "slidy": ".html", "dzslides": ".html",
            "revealjs": ".html", "s5": ".html",

            # Data formats
            "json": ".json", "csv": ".csv", "tsv": ".tsv",

            # Bibliography formats
            "bibtex": ".bib", "biblatex": ".bib", "csljson": ".json",
            "ris": ".ris", "endnotexml": ".xml",

            # Documentation formats
            "man": ".1", "ms": ".ms", "texinfo": ".texi",
            "haddock": ".txt", "pod": ".pod",

            # XML formats
            "docbook": ".xml", "docbook4": ".xml", "docbook5": ".xml",
            "jats": ".xml", "jats_archiving": ".xml",
            "jats_articleauthoring": ".xml", "jats_publishing": ".xml",
            "tei": ".xml", "opml": ".opml",

            # Other formats
            "rtf": ".rtf", "icml": ".icml", "typst": ".typ",
            "native": ".hs", "ipynb": ".ipynb"
        }.get(output_format, ".txt")
        
        output_filename = f"{file_id}_converted{output_extension}"
        output_file = OUTPUT_DIR / output_filename
        
        # Convert document
        success, message = convert_document(input_file, output_file, input_format, output_format, conversion_options)
        
        # Create history entry
        history_entry = ConversionHistory(
            id=file_id,
            filename=original_filename,
            input_format=input_format,
            output_format=output_format,
            timestamp=datetime.now(),
            status="success" if success else "error",
            output_filename=output_filename if success else None,
            error_message=message if not success else None
        )
        
        # Add to history (keep only recent items)
        conversion_history.insert(0, history_entry)
        if len(conversion_history) > MAX_HISTORY_ITEMS:
            conversion_history.pop()
        
        # Clean up old files
        cleanup_old_files()
        
        if success:
            return {
                "status": "success",
                "message": message,
                "output_filename": output_filename,
                "download_url": f"/api/download/{output_filename}"
            }
        else:
            raise HTTPException(status_code=400, detail=message)
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/download/{filename}")
async def download_file(filename: str):
    """Download converted file"""
    file_path = OUTPUT_DIR / filename
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")
    
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/octet-stream'
    )

@app.get("/api/history")
async def get_history():
    """Get conversion history"""
    return [history.dict() for history in conversion_history]

@app.delete("/api/history/{file_id}")
async def delete_history_item(file_id: str):
    """Delete a history item and its files"""
    try:
        # Find and remove from history
        global conversion_history
        conversion_history = [h for h in conversion_history if h.id != file_id]
        
        # Remove files
        for directory in [UPLOAD_DIR, OUTPUT_DIR]:
            for file_path in directory.glob(f"{file_id}*"):
                if file_path.is_file():
                    file_path.unlink()
        
        return {"status": "success", "message": "Item deleted"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/history")
async def clear_history():
    """Clear all conversion history"""
    try:
        global conversion_history
        conversion_history.clear()
        
        # Remove all files
        for directory in [UPLOAD_DIR, OUTPUT_DIR]:
            for file_path in directory.glob("*"):
                if file_path.is_file():
                    file_path.unlink()
        
        return {"status": "success", "message": "History cleared"}
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import sys
    # 检测是否在打包环境中运行
    is_frozen = getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

    if is_frozen:
        # 在打包环境中直接传递 app 对象，禁用 reload
        uvicorn.run(app, host="0.0.0.0", port=8000, reload=False)
    else:
        # 在开发环境中使用字符串引用，启用 reload
        uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
